'use client'

import { motion } from 'framer-motion'
import { Heart, Users, Home, Droplets, GraduationCap, Stethoscope, Globe, Target } from 'lucide-react'

const Projects = () => {
  const projectCategories = [
    {
      icon: Heart,
      title: 'Emergency Relief',
      description: 'Rapid response humanitarian aid for disaster-affected communities.',
      projects: '45+',
      beneficiaries: '150K+',
      gradient: 'from-teal to-teal-dark',
      features: ['Disaster Response', 'Emergency Shelter', 'Food Distribution', 'Medical Aid']
    },
    {
      icon: Droplets,
      title: 'Water & Sanitation',
      description: 'Clean water access and sanitation infrastructure development.',
      projects: '32+',
      beneficiaries: '200K+',
      gradient: 'from-gold to-gold-dark',
      features: ['Well Construction', 'Water Purification', 'Sanitation Systems', 'Hygiene Education']
    },
    {
      icon: GraduationCap,
      title: 'Education Programs',
      description: 'Educational infrastructure and capacity building initiatives.',
      projects: '28+',
      beneficiaries: '75K+',
      gradient: 'from-teal-light to-teal',
      features: ['School Construction', 'Teacher Training', 'Educational Materials', 'Scholarship Programs']
    },
    {
      icon: Stethoscope,
      title: 'Healthcare Access',
      description: 'Healthcare infrastructure and medical service delivery.',
      projects: '22+',
      beneficiaries: '120K+',
      gradient: 'from-gold-light to-gold',
      features: ['Clinic Construction', 'Medical Equipment', 'Health Training', 'Vaccination Programs']
    },
    {
      icon: Home,
      title: 'Housing & Infrastructure',
      description: 'Sustainable housing and community infrastructure development.',
      projects: '18+',
      beneficiaries: '85K+',
      gradient: 'from-teal to-gold',
      features: ['Housing Construction', 'Road Development', 'Community Centers', 'Infrastructure Repair']
    },
    {
      icon: Users,
      title: 'Community Development',
      description: 'Empowerment programs and sustainable livelihood initiatives.',
      projects: '35+',
      beneficiaries: '180K+',
      gradient: 'from-gold to-teal',
      features: ['Skills Training', 'Microfinance', 'Women Empowerment', 'Youth Programs']
    }
  ]

  const globalStats = [
    { icon: Globe, value: '25+', label: 'Countries Served' },
    { icon: Target, value: '200+', label: 'Active Projects' },
    { icon: Users, value: '810K+', label: 'Lives Impacted' },
    { icon: Heart, value: '15+', label: 'Years of Service' }
  ]

  return (
    <section id="projects" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-teal-50 to-gold-50">
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>
        
        {/* Floating Elements */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-teal/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gold/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Humanitarian Projects
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-gold mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Transforming communities through strategic humanitarian initiatives across the globe,
            creating sustainable impact and positive change for those who need it most.
          </p>
        </motion.div>

        {/* Global Impact Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {globalStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="bg-white/60 backdrop-blur-sm border border-white/30 p-6 rounded-xl text-center shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <stat.icon className="w-8 h-8 mx-auto mb-3 text-teal" />
              <div className="text-2xl font-bold text-gray-800 mb-1">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Project Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projectCategories.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className={`bg-gradient-to-br ${category.gradient} rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300`}>
                {/* Header */}
                <div className="p-6 text-white">
                  <category.icon className="w-12 h-12 mb-4" />
                  <h3 className="text-xl font-bold mb-2">{category.title}</h3>
                  <p className="text-white/90 text-sm mb-4">{category.description}</p>
                  
                  {/* Stats */}
                  <div className="flex justify-between text-sm">
                    <div>
                      <div className="font-bold">{category.projects}</div>
                      <div className="text-white/80">Projects</div>
                    </div>
                    <div>
                      <div className="font-bold">{category.beneficiaries}</div>
                      <div className="text-white/80">Beneficiaries</div>
                    </div>
                  </div>
                </div>

                {/* Features */}
                <div className="bg-white/20 backdrop-blur-sm p-4">
                  <div className="grid grid-cols-2 gap-2">
                    {category.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="text-xs text-white/90 bg-white/10 rounded-lg px-2 py-1 text-center"
                      >
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white/60 backdrop-blur-sm border border-white/30 p-8 rounded-2xl max-w-4xl mx-auto shadow-lg">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">
              Ready to Make a Difference?
            </h3>
            <p className="text-gray-600 mb-8 text-lg">
              Join us in creating sustainable impact through humanitarian excellence.
              Together, we can transform communities and change lives.
            </p>
            <motion.button
              className="px-8 py-4 bg-gradient-to-r from-teal to-gold text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const element = document.querySelector('#contact')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' })
                }
              }}
            >
              Start Your Project
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
