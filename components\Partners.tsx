'use client'

import { motion } from 'framer-motion'
import { Users, Globe, Handshake, Award, Building, Heart } from 'lucide-react'

const Partners = () => {
  const partnerCategories = [
    {
      icon: Building,
      title: 'Government Partners',
      description: 'Collaborating with government agencies for large-scale humanitarian initiatives.',
      partners: [
        { name: 'UN Development Programme', type: 'International Organization' },
        { name: 'USAID', type: 'Government Agency' },
        { name: 'European Commission', type: 'Regional Body' },
        { name: 'African Union', type: 'Continental Organization' }
      ],
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Heart,
      title: 'NGO Partners',
      description: 'Working with established NGOs to maximize humanitarian impact.',
      partners: [
        { name: 'Doctors Without Borders', type: 'Medical NGO' },
        { name: 'Oxfam International', type: 'Development NGO' },
        { name: 'Save the Children', type: 'Child Welfare NGO' },
        { name: 'World Vision', type: 'Humanitarian NGO' }
      ],
      gradient: 'from-gold to-gold-dark'
    },
    {
      icon: Handshake,
      title: 'Corporate Partners',
      description: 'Partnering with corporations for sustainable development solutions.',
      partners: [
        { name: 'Microsoft Foundation', type: 'Technology Partner' },
        { name: 'Coca-Cola Foundation', type: 'Corporate Foundation' },
        { name: 'Johnson & Johnson', type: 'Healthcare Partner' },
        { name: 'Unilever Sustainable Living', type: 'Sustainability Partner' }
      ],
      gradient: 'from-teal-light to-gold'
    }
  ]

  const partnershipStats = [
    { icon: Users, value: '150+', label: 'Active Partners' },
    { icon: Globe, value: '25+', label: 'Countries' },
    { icon: Handshake, value: '500+', label: 'Joint Projects' },
    { icon: Award, value: '15+', label: 'Years Partnership' }
  ]

  return (
    <section id="partners" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-teal-50">
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>
        
        {/* Floating Elements */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-gold/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-teal/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Strategic Partners
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-gold mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Building strong partnerships with organizations worldwide to amplify our humanitarian impact
            and create sustainable solutions for communities in need.
          </p>
        </motion.div>

        {/* Partnership Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {partnershipStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm border border-white/50 p-6 rounded-xl text-center shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <stat.icon className="w-8 h-8 mx-auto mb-3 text-teal" />
              <div className="text-2xl font-bold text-gray-800 mb-1">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Partner Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {partnerCategories.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className={`bg-gradient-to-br ${category.gradient} rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300`}>
                {/* Header */}
                <div className="p-6 text-white">
                  <category.icon className="w-12 h-12 mb-4" />
                  <h3 className="text-xl font-bold mb-2">{category.title}</h3>
                  <p className="text-white/90 text-sm mb-6">{category.description}</p>
                </div>

                {/* Partners List */}
                <div className="bg-white/20 backdrop-blur-sm p-6">
                  <div className="space-y-3">
                    {category.partners.map((partner, partnerIndex) => (
                      <motion.div
                        key={partnerIndex}
                        className="bg-white/20 backdrop-blur-sm rounded-lg p-3 border border-white/30"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 + partnerIndex * 0.1, duration: 0.4 }}
                        viewport={{ once: true }}
                      >
                        <div className="text-white font-medium text-sm">{partner.name}</div>
                        <div className="text-white/80 text-xs">{partner.type}</div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Partnership Opportunities */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-white/80 backdrop-blur-sm border border-white/50 p-8 rounded-2xl max-w-4xl mx-auto shadow-lg">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">
              Become Our Partner
            </h3>
            <p className="text-gray-600 mb-8 text-lg">
              Join our network of partners committed to creating positive change.
              Together, we can amplify our impact and reach more communities in need.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-teal to-gold text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  const element = document.querySelector('#contact')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                Partner With Us
              </motion.button>
              <motion.button
                className="px-8 py-4 border-2 border-teal text-teal font-semibold rounded-full hover:bg-teal hover:text-white transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                Learn More
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Partners
